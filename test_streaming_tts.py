#!/usr/bin/env python3
"""
测试流式TTS改造的简单脚本
"""

import asyncio
import json
import re

class MockLLMChat:
    """模拟LLM聊天类，用于测试"""
    
    def __init__(self):
        self.responses = {
            "你好": "你好！很高兴为您服务。\n```json\n{\"internal\": \"用户问候\", \"call\": {\"name\": \"null\"}, \"intent\": \"interaction\"}\n```",
            "搜索手机": "正在为您搜索手机相关商品...\n```json\n{\"internal\": \"执行商品搜索\", \"call\": {\"name\": \"search_products\", \"query\": \"手机\"}, \"intent\": \"search_execute\"}\n```",
            "天气": "正在为您查询天气信息...\n```json\n{\"internal\": \"执行天气查询\", \"call\": {\"name\": \"web_search\", \"query\": \"天气\"}, \"intent\": \"search_execute\"}\n```"
        }
    
    async def stream_chat_with_interrupt(self, prompt, model=None):
        """模拟流式聊天"""
        # 从prompt中提取用户输入
        user_input = ""
        if "用户输入:" in prompt:
            start = prompt.find("用户输入: \"") + len("用户输入: \"")
            end = prompt.find("\"", start)
            if end > start:
                user_input = prompt[start:end]
        
        # 获取对应的响应
        response = self.responses.get(user_input, "我理解了。\n```json\n{\"internal\": \"默认响应\", \"call\": {\"name\": \"null\"}, \"intent\": \"interaction\"}\n```")
        
        # 模拟流式输出
        for char in response:
            yield char
            await asyncio.sleep(0.01)  # 模拟网络延迟

class StreamingTTSProcessor:
    """流式TTS处理器"""
    
    def __init__(self):
        self.chat = MockLLMChat()
    
    async def stream_process_with_immediate_tts(self, prompt: str, transcript: str, conv_id: str):
        """流式处理LLM响应，立即播放TTS内容"""
        try:
            print(f"🎵 开始流式处理，立即播放TTS")
            print(f"📝 用户输入: {transcript}")
            
            # 状态变量
            tts_content = ""
            json_started = False
            tts_sent = False
            
            # 流式获取LLM响应
            full_response = ""
            async for chunk in self.chat.stream_chat_with_interrupt(prompt):
                full_response += chunk
                
                # 检测JSON代码块开始
                if "```json" in full_response and not json_started:
                    json_started = True
                    # 提取TTS内容（JSON代码块之前的内容）
                    json_start_pos = full_response.find("```json")
                    tts_content = full_response[:json_start_pos].strip()
                    
                    # 立即播放TTS内容
                    if tts_content and not tts_sent:
                        print(f"🔊 立即播放TTS: {tts_content}")
                        await self.send_immediate_tts_response(tts_content, conv_id)
                        tts_sent = True
                
                # 如果已经开始JSON块，继续收集JSON内容
                if json_started:
                    # 检测JSON代码块结束
                    if "```" in full_response[full_response.find("```json") + 7:]:
                        # JSON块完整，可以解析
                        break
            
            print(f"🧠 流式处理完成，原始回复: {full_response}")
            
            # 解析JSON部分
            try:
                # 提取JSON内容
                json_start = full_response.find("```json") + 7
                json_end = full_response.find("```", json_start)
                
                if json_start > 6 and json_end > json_start:
                    json_str = full_response[json_start:json_end].strip()
                    compact_result = json.loads(json_str)
                    
                    print(f"✅ JSON解析成功: {compact_result}")
                    
                    # 如果还没有发送TTS（备用方案）
                    if not tts_sent and tts_content:
                        await self.send_immediate_tts_response(tts_content, conv_id)
                    
                    # 处理JSON指令（函数调用等）
                    await self.process_json_instructions(compact_result, transcript, conv_id, tts_content)
                    
                else:
                    raise ValueError("未找到有效的JSON格式")
                    
            except Exception as parse_error:
                print(f"⚠️ JSON解析失败: {parse_error}")
                # 如果JSON解析失败但TTS已发送，至少用户听到了回复
                if not tts_sent:
                    # 尝试从完整响应中提取可能的TTS内容
                    lines = full_response.strip().split('\n')
                    if lines:
                        potential_tts = lines[0].strip()
                        if potential_tts and not potential_tts.startswith('{'):
                            await self.send_immediate_tts_response(potential_tts, conv_id)
                
        except Exception as e:
            print(f"❌ 流式处理出错: {e}")
            # 发送错误回复
            await self.send_immediate_tts_response("抱歉，处理时出现了问题。", conv_id)

    async def send_immediate_tts_response(self, text: str, conv_id: str):
        """立即发送TTS响应，不等待其他处理"""
        try:
            print(f"🔊 [TTS播放] {text}")
            # 模拟TTS播放延迟
            await asyncio.sleep(0.1)
            
        except Exception as e:
            print(f"❌ 发送TTS响应出错: {e}")

    async def process_json_instructions(self, json_result: dict, transcript: str, conv_id: str, tts_content: str):
        """处理JSON指令，执行相应的函数调用"""
        try:
            print(f"🔧 处理JSON指令: {json_result}")
            
            # 提取指令信息
            internal_text = json_result.get('internal', '')
            call_info = json_result.get('call', {})
            intent = json_result.get('intent', 'interaction')
            
            print(f"📝 内部文本: {internal_text}")
            print(f"🔧 函数调用: {call_info}")
            print(f"🎯 意图: {intent}")
            
            # 处理函数调用
            if call_info and isinstance(call_info, dict):
                function_name = call_info.get('name')
                if function_name and function_name != 'null':
                    # 模拟函数调用执行
                    print(f"⚡ 执行函数调用: {function_name}")
                    if function_name == 'search_products':
                        search_query = call_info.get('query', transcript)
                        print(f"🔍 [后台搜索] 正在搜索商品: {search_query}")
                        await asyncio.sleep(0.5)  # 模拟搜索延迟
                        print(f"✅ [搜索完成] 找到了相关商品")
                    elif function_name == 'web_search':
                        web_query = call_info.get('query', transcript)
                        print(f"🌐 [后台搜索] 正在网络搜索: {web_query}")
                        await asyncio.sleep(0.3)  # 模拟搜索延迟
                        print(f"✅ [搜索完成] 获取了相关信息")
            
        except Exception as e:
            print(f"❌ 处理JSON指令出错: {e}")

async def test_streaming_tts():
    """测试流式TTS功能"""
    processor = StreamingTTSProcessor()
    
    test_cases = [
        "你好",
        "搜索手机", 
        "天气",
        "未知输入"
    ]
    
    print("=" * 60)
    print("🧪 开始测试流式TTS改造")
    print("=" * 60)
    
    for i, user_input in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {user_input}")
        print("-" * 40)
        
        # 构建模拟prompt
        prompt = f"""用户输入: "{user_input}"
对话历史: 无
搜索上下文: 无

请分析用户意图并按以下格式回复：

1. 首先直接输出要播放的语音内容（不要任何标记或引号）
2. 然后输出JSON格式的处理信息

请按此格式回复："""
        
        # 执行流式处理
        await processor.stream_process_with_immediate_tts(prompt, user_input, f"conv_{i}")
        
        print("-" * 40)
        await asyncio.sleep(1)  # 间隔
    
    print("\n" + "=" * 60)
    print("✅ 测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(test_streaming_tts())
